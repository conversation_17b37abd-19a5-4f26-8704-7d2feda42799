[gd_scene load_steps=11 format=3 uid="uid://bvx8j0xk3q1h"]

[ext_resource type="Script" path="res://scripts/HighscoreMenu.gd" id="1_highscore"]
[ext_resource type="Texture2D" uid="uid://dr1e1vm8hfr3p" path="res://assets/background.jpg" id="2_background"]
[ext_resource type="FontFile" uid="uid://c3nav2wcuptk" path="res://assets/ARCADECLASSIC.TTF" id="3_font"]
[ext_resource type="AudioStream" uid="uid://cam1ubapfebq" path="res://assets/maou_bgm_piano17.ogg" id="4_bgm"]

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_ButtonHover"]
bg_color = Color(0.3, 0.5, 0.7, 1)
corner_radius_top_left = 8
corner_radius_top_right = 8
corner_radius_bottom_right = 8
corner_radius_bottom_left = 8

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_Button"]
bg_color = Color(0.2, 0.3, 0.5, 1)
corner_radius_top_left = 8
corner_radius_top_right = 8
corner_radius_bottom_right = 8
corner_radius_bottom_left = 8

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_ButtonPressed"]
bg_color = Color(0.1, 0.2, 0.4, 1)
corner_radius_top_left = 8
corner_radius_top_right = 8
corner_radius_bottom_right = 8
corner_radius_bottom_left = 8

[sub_resource type="Theme" id="Theme_HighscoreMenu"]
Button/colors/font_color = Color(1, 1, 1, 1)
Button/colors/font_hover_color = Color(1, 1, 0.8, 1)
Button/colors/font_pressed_color = Color(0.9, 0.9, 0.7, 1)
Button/font_sizes/font_size = 20
Button/fonts/font = ExtResource("3_font")
Button/styles/hover = SubResource("StyleBoxFlat_ButtonHover")
Button/styles/normal = SubResource("StyleBoxFlat_Button")
Button/styles/pressed = SubResource("StyleBoxFlat_ButtonPressed")

[sub_resource type="LabelSettings" id="LabelSettings_Title"]
font = ExtResource("3_font")
font_size = 48
font_color = Color(1, 0.9, 0.3, 1)
outline_size = 3
outline_color = Color(0.2, 0.1, 0, 1)
shadow_size = 2
shadow_color = Color(0, 0, 0, 0.8)
shadow_offset = Vector2(2, 2)

[sub_resource type="LabelSettings" id="LabelSettings_NoScores"]
font = ExtResource("3_font")
font_size = 24
font_color = Color(0.7, 0.7, 0.7, 1)
outline_size = 2
outline_color = Color(0.1, 0.1, 0.1, 1)

[node name="HighscoreMenu" type="Control"]
layout_mode = 3
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2
theme = SubResource("Theme_HighscoreMenu")
script = ExtResource("1_highscore")

[node name="Background" type="TextureRect" parent="."]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2
texture = ExtResource("2_background")
expand_mode = 1
stretch_mode = 6

[node name="MainContainer" type="VBoxContainer" parent="."]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
offset_left = 40.0
offset_top = 40.0
offset_right = -40.0
offset_bottom = -40.0
grow_horizontal = 2
grow_vertical = 2

[node name="TitleContainer" type="VBoxContainer" parent="MainContainer"]
layout_mode = 2

[node name="Title" type="Label" parent="MainContainer/TitleContainer"]
layout_mode = 2
text = "HIGHSCORES"
label_settings = SubResource("LabelSettings_Title")
horizontal_alignment = 1

[node name="TitleSpacer" type="Control" parent="MainContainer"]
custom_minimum_size = Vector2(0, 20)
layout_mode = 2

[node name="ScrollContainer" type="ScrollContainer" parent="MainContainer"]
layout_mode = 2
size_flags_vertical = 3
follow_focus = true

[node name="HighscoreList" type="VBoxContainer" parent="MainContainer/ScrollContainer"]
layout_mode = 2
size_flags_horizontal = 3

[node name="NoScoresLabel" type="Label" parent="MainContainer"]
visible = false
layout_mode = 2
size_flags_vertical = 3
text = "NO HIGHSCORES YET

PLAY THE GAME TO SET YOUR FIRST RECORD!"
label_settings = SubResource("LabelSettings_NoScores")
horizontal_alignment = 1
vertical_alignment = 1

[node name="ButtonContainer" type="HBoxContainer" parent="MainContainer"]
layout_mode = 2
alignment = 1

[node name="BackButton" type="Button" parent="MainContainer/ButtonContainer"]
custom_minimum_size = Vector2(120, 50)
layout_mode = 2
text = "BACK"

[node name="BGMPlayer" type="AudioStreamPlayer" parent="."]
stream = ExtResource("4_bgm")
autoplay = true
